#pragma once
#include "EventLoop.h"
#include "Buffer.h"
#include "Channel.h"
#include "HttpRequest.h"
#include "HttpResponse.h"

//#define MSG_SEND_AUTO

// 发送文件的状态
enum SendFileState
{
    SEND_HEADERS,    // 发送HTTP响应头
    SEND_FILE_DATA,  // 发送文件内容
    SEND_COMPLETE    // 发送完成
};

// 文件发送上下文
struct SendFileContext
{
    int file_fd;           // 文件描述符
    off_t offset;          // 当前发送偏移量
    size_t total_size;     // 文件总大小
    size_t remaining;      // 剩余待发送字节数
    enum SendFileState state;  // 当前发送状态
    char* file_path;       // 文件路径（用于错误处理）
};

struct TcpConnection
{
    struct EventLoop* evLoop;
    struct Channel* channel;
    struct Buffer* readBuf;
    struct Buffer* writeBuf;
    char name[32];
    // http 协议
    struct HttpRequest* request;
    struct HttpResponse* response;
    // 文件发送上下文（用于大文件零拷贝发送）
    struct SendFileContext* sendFileCtx;
};

// 初始化
struct TcpConnection* tcpConnectionInit(int fd, struct EventLoop* evloop);
int tcpConnectionDestroy(void* conn);