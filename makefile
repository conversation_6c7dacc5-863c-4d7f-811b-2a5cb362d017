# 定义编译器
CC = gcc

# 定义编译选项
CFLAGS = -Wall -Wextra -pedantic -std=gnu11

# 添加链接选项以包含 pthread 库
LDFLAGS = -pthread

# 定义源文件和头文件
SOURCES = \
    Buffer.c \
    Channel.c \
    ChannelMap.c \
    EpollDispatcher.c \
    EventLoop.c \
    HttpRequest.c \
    Httpresponse.c \
    main.c \
    PollDispatcher.c \
    SelectDispatcher.c \
    TcpConnection.c \
    TcpServer.c \
    ThreadPool.c \
    WorkerThread.c

HEADERS = \
    Buffer.h \
    Channel.h \
    ChannelMap.h \
    Dispatcher.h \
    EventLoop.h \
    HttpRequest.h \
    HttpResponse.h \
    Log.h \
    TcpConnection.h \
    TcpServer.h \
    ThreadPool.h \
    WorkerThread.h

# 生成目标文件
OBJECTS = $(SOURCES:.c=.o)

# 最终可执行文件名称
EXECUTABLE = reactor_server

# 新的子目录和重命名后的可执行文件名
BIN_DIR = bin
FINAL_EXEC_NAME = reactor_server_exec

# 编译规则
all: $(EXECUTABLE) install

$(EXECUTABLE): $(OBJECTS)
	$(CC) $(CFLAGS) $(LDFLAGS) -o $@ $^

%.o: %.c
	$(CC) $(CFLAGS) -c $< -o $@

# 安装规则：创建目录（如果不存在），然后复制最终的可执行文件到新位置并重命名
install:
	test -d $(BIN_DIR) || mkdir -p $(BIN_DIR)
	cp $(EXECUTABLE) $(BIN_DIR)/$(FINAL_EXEC_NAME)

# 清理规则
clean:
	rm -f $(OBJECTS) $(EXECUTABLE)

# 重新编译并安装
rebuild: clean all

# 默认目标
.PHONY: all clean rebuild install